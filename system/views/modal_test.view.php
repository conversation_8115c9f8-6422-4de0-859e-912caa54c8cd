<?php
use edge\Edge;
?>
        <div class="p-6">
            <h1 class="text-2xl font-bold mb-6">Modal Tab Functionality Test</h1>
            
            <div class="space-y-4">
                <div class="bg-white p-4 rounded-lg shadow">
                    <h2 class="text-lg font-semibold mb-2">Test Tab Behavior</h2>
                    <p class="text-gray-600 mb-4">Click the buttons below to test different tab scenarios:</p>
                    
                    <div class="space-x-2">
                        <button type="button" 
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                                hx-post="<?= APP_ROOT ?>/api/modal_test/test_tab_1"
                                hx-target="#modal_body"
                                hx-swap="innerHTML"
                                @click="showModal = true">
                            Open Test Tab 1 (Fresh Modal)
                        </button>
                        
                        <button type="button" 
                                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                                hx-post="<?= APP_ROOT ?>/api/system/user_modal"
                                hx-target="#modal_body"
                                hx-swap="innerHTML"
                                @click="showModal = true">
                            Open User Modal (Fresh Modal)
                        </button>
                    </div>
                </div>
                
                <div class="bg-white p-4 rounded-lg shadow">
                    <h2 class="text-lg font-semibold mb-2">Expected Behavior</h2>
                    <ul class="list-disc list-inside space-y-2 text-gray-700">
                        <li><strong>When modal is closed:</strong> Opening content should replace the default tab</li>
                        <li><strong>When modal is open:</strong> Opening new content should create a new tab and switch to it</li>
                        <li><strong>Pin functionality:</strong> Click the pin icon to pin tabs - pinned tabs override the first rule</li>
                        <li><strong>Close tabs:</strong> Click the X to close tabs (cannot close the last tab)</li>
                        <li><strong>Switch tabs:</strong> Click tab titles to switch between tabs</li>
                    </ul>
                </div>
                
                <div class="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    <h2 class="text-lg font-semibold mb-2 text-yellow-800">Testing Instructions</h2>
                    <ol class="list-decimal list-inside space-y-1 text-yellow-700">
                        <li>Click "Open Test Tab 1" - should open modal with single tab</li>
                        <li>Click "Open Test Tab 2" button inside the modal - should create new tab</li>
                        <li>Try pinning a tab using the pin icon</li>
                        <li>Close the modal and open a new tab - should respect pinned tabs</li>
                        <li>Test closing tabs with the X button</li>
                    </ol>
                </div>
            </div>
        </div>
